import { defaultStrategyConfig, strategyMap } from '../strategies'

export class SignalEngine {
	private readonly candles: Candle[] = []
	private readonly strategyFn: (candles: Candle[], config: StrategyConfig, lastSignal: Signal) => SignalMeta
	private readonly config: StrategyConfig
	private readonly signalHistory: SignalMeta[] = []
	private lastSignal: Signal = undefined
	private lastMeta: SignalMeta = { signal: undefined, reason: 'Not evaluated yet', confidence: 'low' }

	constructor(strategyName: StrategyName, config?: Partial<StrategyConfig>) {
		const fn = strategyMap[strategyName]
		if (!fn) throw new Error(`Unknown strategy: ${strategyName}`)
		this.strategyFn = fn
		this.config = { ...defaultStrategyConfig[strategyName], ...config }
	}

	update(candle: Candle): SignalMeta {
		this.candles.push(candle)
		const result = this.strategyFn(this.candles, this.config, this.lastSignal)
		if (result.signal !== this.lastSignal) {
			this.lastSignal = result.signal
		} else {
			result.signal = 'HOLD'
			result.reason += ' (no change)'
		}
		this.lastMeta = result
		this.signalHistory.push(result)
		return result
	}

	getLastSignal(): Signal {
		return this.lastSignal
	}

	getLastMeta(): SignalMeta {
		return this.lastMeta
	}

	getSignalHistory(): SignalMeta[] {
		return this.signalHistory
	}

	reset(): void {
		this.candles.length = 0
		this.signalHistory.length = 0
		this.lastSignal = undefined
		this.lastMeta = { signal: undefined, reason: 'Reset', confidence: 'low' }
	}

	evaluatePerformance(correctSignals: Signal[], actualOutcomes: Signal[]): { accuracy: number } {
		let correct = 0
		const count = Math.min(correctSignals.length, actualOutcomes.length)
		for (let i = 0; i < count; i++) {
			if (correctSignals[i] === actualOutcomes[i]) correct++
		}
		return { accuracy: count > 0 ? correct / count : 0 }
	}

	static resolvePrioritySignal(signals: SignalMeta[], priorities: number[]): SignalMeta {
		let bestIndex = -1
		for (let i = 0; i < signals.length; i++) {
			const signal = signals[i]
			if (!signal || signal.signal === 'HOLD' || signal.signal === undefined) continue
			if (
				bestIndex === -1 ||
				(priorities[i] ?? 0) > (priorities[bestIndex] ?? 0) ||
				(priorities[i] ?? 0) === (priorities[bestIndex] ?? 0)
			) {
				bestIndex = i
			}
		}
		return bestIndex >= 0 ? signals[bestIndex]! : { signal: 'HOLD', reason: 'No dominant signal', confidence: 'low' }
	}
}
