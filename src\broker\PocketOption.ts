import { io, type Socket } from 'socket.io-client'
import { Region } from '../constants'
import { extractAuth } from '../utils/parsers'
import { logger } from '../utils/logger'
import { expiryToSeconds, formatData } from '../utils/formatter'
import { Order } from './channels/order'
import { Candles } from './channels/candles'
import { SelectAsset } from './channels/selectAsset'
import { SignalEngine } from '../core/SignalEngine'

export class PocketOption {
	private ws?: Socket
	private balance: number = 0
	private isConnected: boolean = false
	private connectionTimeout: number = 10000 // 10 seconds
	private heartbeatInterval: NodeJS.Timeout | null = null
	private signalEngine: SignalEngine = new SignalEngine('SMA_CROSS')
	private candleMap = new Map<string, Candle>()

	private orderPayload: OrderPayload | null = null
	private orderResult: OrderResult | null = null
	private chartSettings: ChartSettings | null = null

	private currentCandle: Partial<Candle> | null = null
	private candleBufferInterval: number = 30 // in seconds
	private candleFlushTimer: NodeJS.Timeout | null = null

	private signalEngines: Map<number, SignalEngine> = new Map()
	private candleBuckets: Map<number, Partial<Candle> | null> = new Map()

	private signalHandlers: Map<number, (meta: SignalMeta) => void> = new Map()

	constructor(private ssID: string, private demo: boolean = true) {}

	connect(): Promise<void> {
		return new Promise((resolve, reject) => {
			const endpoint = this.demo ? Region.DEMO_REGION : Region.getRegion()[0]
			const options = Region.SOCKET_OPTIONS
			const auth = extractAuth(this.ssID)

			if (!auth) {
				return reject(new Error('Invalid ssID, authentication failed.'))
			}

			const { session, uid } = auth

			// Set connection timeout
			const timeout = setTimeout(() => {
				if (this.ws) {
					this.ws.disconnect()
				}
				reject(new Error('Connection timeout'))
			}, this.connectionTimeout)

			this.ws = io(endpoint, options)

			this.ws.once('connect', () => {
				if (this.ws) {
					this.ws.emit('auth', {
						isDemo: this.demo ? 1 : 0,
						isFastHistory: true,
						platform: 2,
						session,
						uid
					})
				}
			})

			this.ws.once('successauth', () => {
				clearTimeout(timeout)
				this.isConnected = true
				logger.success(`Broker`, `Authenticated successfully`)

				// Set up balance update listener
				this.setupListeners()

				// Start heartbeat
				this.startHeartbeat()

				// Start candle aggregation
				this.startCandleAggregation()

				resolve()
			})

			// this.ws.on('updateStream', (data: unknown[]) => {})

			// Debug events
			this.ws.onAny((event: string, ...args: unknown[]) => {
				logger.debug(`Broker`, `Received event: ${event}`)
			})

			this.ws.on('disconnect', () => {
				this.disconnect()
				logger.warn(`Broker`, `Disconnected from server`)
			})

			this.ws.on('error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Error`, err)
				reject(err)
			})

			this.ws.on('connect_error', (err: Error) => {
				clearTimeout(timeout)
				this.isConnected = false
				logger.error(`Broker`, `Connection error`, err)
				reject(err)
			})
		})
	}

	async emit(event: string, data: unknown): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.emit(event, data)
			resolve()
		})
	}

	async on(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.on(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
			})
			resolve()
		})
	}

	async once(event: string, callback: (data: unknown[]) => void): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			if (!this.ws) {
				return reject(new Error('Not connected to server'))
			}

			this.ws.once(event, (data: unknown[]) => {
				const parsedData = formatData(data) as unknown[]
				callback(parsedData)
				resolve()
			})
		})
	}

	async getBalance(): Promise<number> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise((resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			if (this.balance !== 0) {
				resolve(this.balance)
			} else {
				this.once('successupdateBalance', (data: unknown) => {
					const balanceData = data as BalanceData

					this.balance = balanceData.balance
					resolve(balanceData.balance)
				})
			}
		})
	}

	async placeOrder(payload: OrderPayload): Promise<void> {
		return new Promise<void>(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			Order.init(this)
			await Order.call(payload)

			resolve()
		})
	}

	async getCandles(assetSymbol: string): Promise<unknown[]> {
		if (!this.ws || !this.isConnected) {
			throw new Error('Not connected to server')
		}

		return new Promise<unknown[]>(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			Candles.init(this)

			// Wait for chart settings to be available
			const chartSettings = await this.waitForChartSettings()

			// This triggers the event `loadHistoryPeriod` which then responses with `loadHistoryPeriodFast`
			await Candles.call(assetSymbol, chartSettings)

			this.once('loadHistoryPeriodFast', (data: unknown[]) => {
				console.log(JSON.stringify(data)) // Data where asset: 'USDARS_otc', period: 30, data: [...] came from
				resolve(data as unknown[])
			})
		})
	}

	async selectAsset(assetSymbol: string, chartPeriod: number | string): Promise<{}> {
		return new Promise(async (resolve, reject) => {
			if (!this.ws || !this.isConnected) {
				return reject(new Error('Not connected to server'))
			}

			SelectAsset.init(this)

			this.signalEngine

			// This event, when called will trigger the `updateStream` to start ticking
			await SelectAsset.call(assetSymbol, chartPeriod)
			resolve({ assetSymbol, chartPeriod })
		})
	}

	/**
	 * Check if the broker is connected
	 */
	getConnectionStatus(): boolean {
		return this.isConnected && this.ws?.connected === true
	}

	getChartSettings(): ChartSettings | null {
		logger.warn(`Broker`, `Return chart settings`)
		return this.chartSettings
	}

	/**
	 * Wait for chart settings to be received from the server
	 * @param timeout Maximum time to wait in milliseconds (default: 1000ms)
	 * @returns Promise that resolves with chart settings
	 */
	private async waitForChartSettings(timeout: number = 1000): Promise<ChartSettings> {
		// If chart settings are already available, return them immediately
		if (this.chartSettings) {
			return this.chartSettings
		}

		return new Promise<ChartSettings>((resolve, reject) => {
			const timeoutId = setTimeout(() => {
				reject(new Error('Timeout waiting for chart settings'))
			}, timeout)

			// Set up a listener for chart settings updates
			const checkSettings = () => {
				if (this.chartSettings) {
					clearTimeout(timeoutId)
					resolve(this.chartSettings)
				} else {
					// Check again in 100ms
					setTimeout(checkSettings, 100)
				}
			}

			// Start checking
			checkSettings()
		})
	}

	/**
	 * Disconnect from the broker
	 */
	disconnect(): void {
		if (this.ws) {
			this.stopHeartbeat()
			this.ws.disconnect()
			this.isConnected = false
			logger.info('Broker', 'Disconnected from server')
		}

		if (this.candleFlushTimer) {
			clearInterval(this.candleFlushTimer)
			this.candleFlushTimer = null
		}
	}

	/**
	 * Manually update the balance (useful for testing or external balance updates)
	 */
	updateBalance(newBalance: number): void {
		const oldBalance = this.balance
		this.balance = newBalance
		logger.info('Broker', `Balance manually updated: ${oldBalance} -> ${this.balance}`)
	}

	registerSignalEngine(expirySec: number, engine: SignalEngine, onSignal?: (meta: SignalMeta) => void) {
		const expiry = expiryToSeconds(expirySec)
		this.signalEngines.set(expiry, engine)
		this.candleBuckets.set(expiry, null)

		if (onSignal) {
			this.signalHandlers.set(expiry, onSignal)
		}
	}

	/**
	 * Set up listener for balance updates
	 */
	private setupListeners(): void {
		if (!this.ws) return

		this.ws.on('updateCharts', this.handleChartSettings)
		this.ws.on('updateStream', this.handleUpdateStream)
	}

	private handleUpdateStream = (data: unknown[]): void => {
		const parsedData = formatData(data) as [string, number, number][]

		for (const [symbol, timestamp, price] of parsedData) {
			for (const [expiry, engine] of this.signalEngines.entries()) {
				const expirySec = expiryToSeconds(expiry)
				const candleTime = Math.floor(timestamp / expirySec) * expirySec
				let bucket = this.candleBuckets.get(expirySec)

				if (!bucket || bucket.time !== candleTime) {
					if (bucket) {
						const finalized: Candle = {
							time: bucket.time!,
							open: bucket.open!,
							high: bucket.high!,
							low: bucket.low!,
							close: bucket.close!
						}

						if (this.isCandleClosed(finalized, expirySec)) {
							const signal = engine.update(finalized)
							const handler = this.signalHandlers.get(expirySec)
							if (handler) handler(signal)

							// You can place a trade here
							logger.info(`SignalEngine [${expirySec}s]`, `${signal.signal} - ${signal.reason}`)
						}
					}

					// Create new bucket for new candle
					bucket = {
						time: candleTime,
						open: price,
						high: price,
						low: price,
						close: price
					}
				} else {
					// Update bucket with new price
					bucket.high = Math.max(bucket.high!, price)
					bucket.low = Math.min(bucket.low!, price)
					bucket.close = price
				}
				this.candleBuckets.set(expirySec, bucket)
			}
		}
	}

	private handleChartSettings = (data: unknown[]): void => {
		const parsedData = formatData(data) as ChartsData

		if (Array.isArray(parsedData) && parsedData.length > 0) {
			if (parsedData[0] && typeof parsedData[0].settings === 'object') {
				this.chartSettings = parsedData[0].settings as ChartSettings
			} else if (parsedData[0] && typeof parsedData[0].settings === 'string') {
				this.chartSettings = JSON.parse(parsedData[0].settings) as ChartSettings
			}
		}

		if (!this.chartSettings) {
			logger.error(`Broker`, `Failed to parse chart settings`)
			return
		}

		logger.success(`Broker`, `Received chart settings`)
	}

	private startHeartbeat(): void {
		if (!this.ws) return

		// Send a ping every 20 seconds
		this.heartbeatInterval = setInterval(() => {
			if (this.ws) {
				this.ws.emit('ps')
			}
		}, 20000)
	}

	private stopHeartbeat(): void {
		if (!this.heartbeatInterval) return

		clearInterval(this.heartbeatInterval)
		this.heartbeatInterval = null
	}

	private startCandleAggregation(): void {
		if (this.candleFlushTimer) return

		this.candleFlushTimer = setInterval(() => {
			if (this.currentCandle) {
				const filled: Candle = {
					time: this.currentCandle.time!,
					open: this.currentCandle.open!,
					high: this.currentCandle.high!,
					low: this.currentCandle.low!,
					close: this.currentCandle.close!
				}
				const signal = this.signalEngine.update(filled)
				logger.info('SignalEngine', `${signal.signal} - ${signal.reason}`)
				this.currentCandle = null
			}
		}, this.candleBufferInterval * 1000)
	}

	// Create or update a candle in the map
	private createOrUpdateCandle = (symbol: string, timestamp: number, price: number): Candle => {
		const key = symbol
		const time = Math.floor(timestamp / 30) * 30 // if expiry = 30s
		const prev = this.candleMap.get(key)

		if (!prev || prev.time !== time) {
			const newCandle: Candle = { time, open: price, high: price, low: price, close: price }
			this.candleMap.set(key, newCandle)
			return newCandle
		} else {
			prev.high = Math.max(prev.high, price)
			prev.low = Math.min(prev.low, price)
			prev.close = price
			return prev
		}
	}
	// Check if a candle is closed based on its timestamp and the current time and expiry, default expiry is 30s
	private isCandleClosed = (candle: Candle, expiry: number = 30): boolean => {
		const now = Date.now() / 1000
		return now > candle.time + expiry
	}
}
