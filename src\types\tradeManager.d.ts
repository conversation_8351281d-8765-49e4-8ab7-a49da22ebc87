interface TradeOutcome {
	signal: Signal
	result: 'win' | 'loss'
}

interface TradeSession {
	steps: number
	lastTradeTime: number
	currentStake: number
	totalProfit: number
	totalLoss: number
	totalWins: number
	totalTrades: number
}

interface TradeManagerOptions {
	baseStake: number
	martingaleFactor: number
	expiry: string
	cooldownMs: number
	maxSteps: number
	maxLoss?: number
	stopWin?: number
	asset: string
	directionMap?: Record<Signal, 'call' | 'put'>
	placeOrder: (payload: OrderPayload) => Promise<void>
}
