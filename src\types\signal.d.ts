type Signal = 'BUY' | 'SELL' | 'HOLD' | undefined
type StrategyFn = (candles: Candle[], config: any, lastSignal: Signal) => SignalMeta
type StrategyConfig = Record<string, any>

interface Candle {
	time: number
	open: number
	high: number
	low: number
	close: number
}

type StrategyName =
	| 'SMA_CROSS'
	| 'RSI_BB_SMA'
	| 'RSI_MACD_BB_STOCH'
	| 'MACD_KC_CCI'
	| 'MACD_BB_STOCH'
	| 'MACD_KC_STOCH'
	| 'MACD_KC'
	| 'MACD_STOCH'
	| 'BB_STOCH'
	| 'KC_STOCH'
	| 'KC_CCI'
	| 'STOCH_CCI'
	| 'CCI_STOCH'
	| 'STOCH_MACD'

interface SignalMeta {
	signal: Signal
	reason: string
	confidence: 'low' | 'medium' | 'high'
}

interface SmaCrossConfig {
	fastPeriod: number
	slowPeriod: number
}

interface RsiBbSmaConfig {
	rsiPeriod: number
	bbPeriod: number
	smaPeriod: number
}

interface RsiMacdBbStochConfig {
	rsiPeriod: number
	bbPeriod: number
	macdFast: number
	macdSlow: number
	macdSignal: number
	stochPeriod: number
	stochSignal: number
}

interface MacdKcCciConfig {
	macdFast: number
	macdSlow: number
	macdSignal: number
	kcPeriod: number
	kcMultiplier: number
	cciPeriod: number
}

// Two-indicator strategy configurations
interface MacdKcConfig {
	macdFast: number
	macdSlow: number
	macdSignal: number
	kcPeriod: number
	kcMultiplier: number
}

interface MacdStochConfig {
	macdFast: number
	macdSlow: number
	macdSignal: number
	stochPeriod: number
	stochSignal: number
}

interface BbStochConfig {
	bbPeriod: number
	stochPeriod: number
	stochSignal: number
}

interface KcStochConfig {
	kcPeriod: number
	kcMultiplier: number
	stochPeriod: number
	stochSignal: number
}

interface KcCciConfig {
	kcPeriod: number
	kcMultiplier: number
	cciPeriod: number
}

interface StochCciConfig {
	stochPeriod: number
	stochSignal: number
	cciPeriod: number
}

interface CciStochConfig {
	cciPeriod: number
	stochPeriod: number
	stochSignal: number
}

interface StochMacdConfig {
	stochPeriod: number
	stochSignal: number
	macdFast: number
	macdSlow: number
	macdSignal: number
}

// Three-indicator strategy configurations
interface MacdBbStochConfig {
	macdFast: number
	macdSlow: number
	macdSignal: number
	bbPeriod: number
	stochPeriod: number
	stochSignal: number
}

interface MacdKcStochConfig {
	macdFast: number
	macdSlow: number
	macdSignal: number
	kcPeriod: number
	kcMultiplier: number
	stochPeriod: number
	stochSignal: number
}
