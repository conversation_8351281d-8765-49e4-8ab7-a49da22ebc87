import { sma } from '../indicators/sma'

export function evaluateSmaCross(candles: Candle[], config: SmaCrossConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)
	const { fastPeriod, slowPeriod } = config

	const fast = sma(closes, fastPeriod)
	const slow = sma(closes, slowPeriod)

	const i = closes.length - 1
	if (i < 1) return { signal: undefined, reason: 'Not enough candles', confidence: 'low' }

	const f = fast[i],
		s = slow[i]
	const pf = fast[i - 1],
		ps = slow[i - 1]

	if (f === undefined || s === undefined || pf === undefined || ps === undefined) {
		return { signal: undefined, reason: 'Incomplete SMA values', confidence: 'low' }
	}

	const strength = Math.abs(f - s)
	let confidence: SignalMeta['confidence'] = 'medium'

	if (strength > 0.2) confidence = 'high'
	else if (strength > 0.05) confidence = 'medium'

	if (pf < ps && f > s) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `Fast SMA crossed above slow SMA by ${strength.toFixed(4)}`,
				confidence
			}
		}
	}

	if (pf > ps && f < s) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `Fast SMA crossed below slow SMA by ${strength.toFixed(4)}`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'No SMA crossover or duplicate signal', confidence }
}
