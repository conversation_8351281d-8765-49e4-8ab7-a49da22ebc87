import { KeltnerChannels, Stochastic } from 'technicalindicators'

export function evaluateKcStoch(candles: Candle[], config: KcStochConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const kc = KeltnerChannels.calculate({
		maPeriod: config.kcPeriod,
		atrPeriod: 10,
		useSMA: false,
		multiplier: config.kcMultiplier,
		high: highs,
		low: lows,
		close: closes
	})

	const stoch = Stochastic.calculate({
		period: config.stochPeriod,
		signalPeriod: config.stochSignal,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const ki = kc.length - 1
	const si = stoch.length - 1

	if (ki < 0 || si < 1) {
		return { signal: undefined, reason: 'Insufficient indicator data', confidence: 'low' }
	}

	const price = closes[i]
	const lastKC = kc[ki]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastKC == null || lastStoch == null || prevStoch == null) {
		return { signal: undefined, reason: 'Incomplete indicator values', confidence: 'low' }
	}
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) {
		return { signal: undefined, reason: 'Incomplete Stochastic values', confidence: 'low' }
	}

	// Calculate strength based on KC position and Stochastic momentum
	const kcPosition = Math.abs(price - (lastKC.upper + lastKC.lower) / 2) / ((lastKC.upper - lastKC.lower) / 2) // KC position
	const stochMomentum = Math.abs(lastStoch.k - prevStoch.k) / 100 // Stochastic momentum
	const stochPosition = Math.abs(lastStoch.k - 50) / 50 // Stochastic position from neutral

	// Combined strength metric
	const strength = kcPosition * 0.4 + stochMomentum * 0.35 + stochPosition * 0.25

	// Determine confidence based on strength
	let confidence: SignalMeta['confidence'] = 'medium'
	if (strength > 0.13) confidence = 'high'
	else if (strength > 0.07) confidence = 'medium'
	else confidence = 'low'

	// Price near/below lower KC + Stochastic oversold and turning up
	const nearLowerKC = price <= lastKC.lower * 1.01 // Allow 1% tolerance
	const stochOversoldTurning = lastStoch.k < 30 && lastStoch.k > prevStoch.k && lastStoch.k > lastStoch.d

	// Price near/above upper KC + Stochastic overbought and turning down
	const nearUpperKC = price >= lastKC.upper * 0.99 // Allow 1% tolerance
	const stochOverboughtTurning = lastStoch.k > 70 && lastStoch.k < prevStoch.k && lastStoch.k < lastStoch.d

	if (nearLowerKC && stochOversoldTurning) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `Price near lower KC + Stochastic oversold turning up (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	if (nearUpperKC && stochOverboughtTurning) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `Price near upper KC + Stochastic overbought turning down (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'No crossover or duplicate signal', confidence }
}
