import { KeltnerChannels, CCI, MACD } from 'technicalindicators'

export function evaluateMacdKcCci(candles: Candle[], config: MacdKcCciConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const kc = KeltnerChannels.calculate({
		maPeriod: config.kcPeriod,
		atrPeriod: 10,
		useSMA: false,
		multiplier: config.kcMultiplier,
		high: highs,
		low: lows,
		close: closes
	})

	const cci = CCI.calculate({
		period: config.cciPeriod,
		high: highs,
		low: lows,
		close: closes
	})

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: config.macdFast,
		slowPeriod: config.macdSlow,
		signalPeriod: config.macdSignal,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	if (kc.length < 1 || cci.length < 1 || macd.length < 2) {
		return { signal: undefined, reason: 'Insufficient indicator data', confidence: 'low' }
	}

	const price = closes.at(-1)!
	const lastKC = kc.at(-1)!
	const lastCCI = cci.at(-1)!
	const lastMACD = macd.at(-1)!

	// Check if MACD values are defined before proceeding
	if (lastMACD.MACD == null || lastMACD.signal == null) {
		return { signal: undefined, reason: 'Incomplete MACD values', confidence: 'low' }
	}

	// Calculate strength based on MACD histogram magnitude, KC position, and CCI extremes
	const macdHistogram = Math.abs(lastMACD.MACD - lastMACD.signal) / price // MACD histogram magnitude
	const kcPosition = Math.abs(price - (lastKC.upper + lastKC.lower) / 2) / ((lastKC.upper - lastKC.lower) / 2) // KC position
	const cciExtreme = Math.abs(lastCCI) / 200 // CCI extreme level (normalized to 200 range)

	// Combined strength metric
	const strength = macdHistogram * 0.4 + kcPosition * 0.35 + cciExtreme * 0.25

	// Determine confidence based on strength
	let confidence: SignalMeta['confidence'] = 'medium'
	if (strength > 0.18) confidence = 'high'
	else if (strength > 0.1) confidence = 'medium'
	else confidence = 'low'

	if (price < lastKC.lower && lastCCI < -100 && lastMACD.MACD > lastMACD.signal) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `Price below KC + CCI low + MACD bullish (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	if (price > lastKC.upper && lastCCI > 100 && lastMACD.MACD < lastMACD.signal) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `Price above KC + CCI high + MACD bearish (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'Conditions not met or duplicate signal', confidence }
}
