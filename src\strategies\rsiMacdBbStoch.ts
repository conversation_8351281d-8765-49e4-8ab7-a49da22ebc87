import { RSI, MACD, BollingerBands, Stochastic } from 'technicalindicators'

export function evaluateRsiMacdBbStoch(
	candles: Candle[],
	config: RsiMacdBbStochConfig,
	lastSignal: Signal
): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const rsi = RSI.calculate({ period: config.rsiPeriod, values: closes })
	const bb = BollingerBands.calculate({ period: config.bbPeriod, stdDev: 2, values: closes })
	const macd = MACD.calculate({
		values: closes,
		fastPeriod: config.macdFast,
		slowPeriod: config.macdSlow,
		signalPeriod: config.macdSignal,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})
	const stoch = Stochastic.calculate({
		period: config.stochPeriod,
		signalPeriod: config.stochSignal,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	if (rsi.length < 1 || bb.length < 1 || macd.length < 2 || stoch.length < 2) {
		return { signal: undefined, reason: 'Insufficient indicator data', confidence: 'low' }
	}

	const price = closes[i]
	const lastRSI = rsi.at(-1)!
	const lastBB = bb.at(-1)!
	const lastMACD = macd.at(-1)!
	const lastStoch = stoch.at(-1)!

	// Check if any values are undefined before proceeding
	if (price == null || lastRSI == null || lastBB == null || lastMACD == null || lastStoch == null) {
		return { signal: undefined, reason: 'Incomplete indicator values', confidence: 'low' }
	}
	if (lastBB.lower == null || lastBB.upper == null) {
		return { signal: undefined, reason: 'Incomplete Bollinger Bands values', confidence: 'low' }
	}
	if (lastMACD.MACD == null || lastMACD.signal == null) {
		return { signal: undefined, reason: 'Incomplete MACD values', confidence: 'low' }
	}
	if (lastStoch.k == null || lastStoch.d == null) {
		return { signal: undefined, reason: 'Incomplete Stochastic values', confidence: 'low' }
	}

	// Calculate multi-indicator strength
	const rsiDeviation = Math.abs(lastRSI - 50) / 50 // RSI deviation from neutral
	const macdHistogram = Math.abs(lastMACD.MACD - lastMACD.signal) / price // MACD histogram magnitude
	const bbPosition = Math.abs(price - (lastBB.upper + lastBB.lower) / 2) / ((lastBB.upper - lastBB.lower) / 2) // BB position
	const stochMomentum = Math.abs(lastStoch.k - 50) / 50 // Stochastic momentum from neutral

	// Combined strength metric with weighted components
	const strength = rsiDeviation * 0.25 + macdHistogram * 0.3 + bbPosition * 0.25 + stochMomentum * 0.2

	// Determine confidence based on strength
	let confidence: SignalMeta['confidence'] = 'medium'
	if (strength > 0.2) confidence = 'high'
	else if (strength > 0.1) confidence = 'medium'
	else confidence = 'low'

	if (lastRSI < 30 && price < lastBB.lower && lastMACD.MACD > lastMACD.signal && lastStoch.k < 20) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `RSI low + BB low + MACD bullish + Stoch low (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	if (lastRSI > 70 && price > lastBB.upper && lastMACD.MACD < lastMACD.signal && lastStoch.k > 80) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `RSI high + BB high + MACD bearish + Stoch high (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'No crossover or duplicate signal', confidence }
}
