import { BollingerBands, Stochastic } from 'technicalindicators'

export function evaluateBbStoch(candles: Candle[], config: BbStochConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const bb = BollingerBands.calculate({
		period: config.bbPeriod,
		stdDev: 2,
		values: closes
	})

	const stoch = Stochastic.calculate({
		period: config.stochPeriod,
		signalPeriod: config.stochSignal,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const bi = bb.length - 1
	const si = stoch.length - 1

	if (bi < 0 || si < 1) {
		return { signal: undefined, reason: 'Insufficient indicator data', confidence: 'low' }
	}

	const price = closes[i]
	const lastBB = bb[bi]
	const lastStoch = stoch[si]
	const prevStoch = stoch[si - 1]

	// Check if any values are undefined before proceeding
	if (price == null || lastBB == null || lastStoch == null || prevStoch == null) {
		return { signal: undefined, reason: 'Incomplete indicator values', confidence: 'low' }
	}
	if (lastStoch.k == null || lastStoch.d == null || prevStoch.k == null || prevStoch.d == null) {
		return { signal: undefined, reason: 'Incomplete Stochastic values', confidence: 'low' }
	}

	// Calculate strength based on BB squeeze and Stochastic momentum
	const bbWidth = (lastBB.upper - lastBB.lower) / price // BB width (squeeze indicator)
	const bbPosition = Math.abs(price - (lastBB.upper + lastBB.lower) / 2) / ((lastBB.upper - lastBB.lower) / 2) // BB position
	const stochMomentum = Math.abs(lastStoch.k - prevStoch.k) / 100 // Stochastic momentum
	const stochPosition = Math.abs(lastStoch.k - 50) / 50 // Stochastic position from neutral

	// Combined strength metric
	const strength = bbWidth * 0.25 + bbPosition * 0.35 + stochMomentum * 0.2 + stochPosition * 0.2

	// Determine confidence based on strength
	let confidence: SignalMeta['confidence'] = 'medium'
	if (strength > 0.14) confidence = 'high'
	else if (strength > 0.07) confidence = 'medium'
	else confidence = 'low'

	// Price touching lower BB + Stochastic oversold and %K crossing above %D
	const touchingLowerBB = price <= lastBB.lower * 1.005 // Allow 0.5% tolerance
	const stochOversoldCross = lastStoch.k < 30 && prevStoch.k <= prevStoch.d && lastStoch.k > lastStoch.d

	// Price touching upper BB + Stochastic overbought and %K crossing below %D
	const touchingUpperBB = price >= lastBB.upper * 0.995 // Allow 0.5% tolerance
	const stochOverboughtCross = lastStoch.k > 70 && prevStoch.k >= prevStoch.d && lastStoch.k < lastStoch.d

	if (touchingLowerBB && stochOversoldCross) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `Price touching lower BB + Stochastic oversold crossover (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	if (touchingUpperBB && stochOverboughtCross) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `Price touching upper BB + Stochastic overbought crossover (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'No crossover or duplicate signal', confidence }
}
