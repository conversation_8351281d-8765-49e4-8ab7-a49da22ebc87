import { MACD, KeltnerChannels } from 'technicalindicators'

export function evaluateMacdKc(candles: Candle[], config: MacdKcConfig, lastSignal: Signal): SignalMeta {
	const closes = candles.map(c => c.close)
	const highs = candles.map(c => c.high)
	const lows = candles.map(c => c.low)

	const macd = MACD.calculate({
		values: closes,
		fastPeriod: config.macdFast,
		slowPeriod: config.macdSlow,
		signalPeriod: config.macdSignal,
		SimpleMAOscillator: false,
		SimpleMASignal: false
	})

	const kc = KeltnerChannels.calculate({
		maPeriod: config.kcPeriod,
		atrPeriod: 10,
		useSMA: false,
		multiplier: config.kcMultiplier,
		high: highs,
		low: lows,
		close: closes
	})

	const i = closes.length - 1
	const mi = macd.length - 1
	const ki = kc.length - 1

	if (mi < 1 || ki < 0) {
		return { signal: undefined, reason: 'Insufficient indicator data', confidence: 'low' }
	}

	const price = closes[i]
	const lastMACD = macd[mi]
	const prevMACD = macd[mi - 1]
	const lastKC = kc[ki]

	// Check if any values are undefined before proceeding
	if (price == null || lastMACD == null || prevMACD == null || lastKC == null) {
		return { signal: undefined, reason: 'Incomplete indicator values', confidence: 'low' }
	}
	if (lastMACD.MACD == null || lastMACD.signal == null || prevMACD.MACD == null || prevMACD.signal == null) {
		return { signal: undefined, reason: 'Incomplete MACD values', confidence: 'low' }
	}

	// Calculate strength based on MACD crossover magnitude and KC position
	const macdCrossoverMagnitude = Math.abs(lastMACD.MACD - lastMACD.signal) / price // MACD crossover strength
	const kcPosition = Math.abs(price - (lastKC.upper + lastKC.lower) / 2) / ((lastKC.upper - lastKC.lower) / 2) // KC position

	// Combined strength metric
	const strength = macdCrossoverMagnitude * 0.6 + kcPosition * 0.4

	// Determine confidence based on strength
	let confidence: SignalMeta['confidence'] = 'medium'
	if (strength > 0.15) confidence = 'high'
	else if (strength > 0.08) confidence = 'medium'
	else confidence = 'low'

	// MACD bullish crossover + price near or below lower Keltner Channel
	const macdBullishCross = prevMACD.MACD <= prevMACD.signal && lastMACD.MACD > lastMACD.signal
	const nearLowerKC = price <= lastKC.lower * 1.01 // Allow 1% tolerance

	// MACD bearish crossover + price near or above upper Keltner Channel
	const macdBearishCross = prevMACD.MACD >= prevMACD.signal && lastMACD.MACD < lastMACD.signal
	const nearUpperKC = price >= lastKC.upper * 0.99 // Allow 1% tolerance

	if (macdBullishCross && nearLowerKC) {
		if (lastSignal !== 'BUY') {
			return {
				signal: 'BUY',
				reason: `MACD bullish crossover + price near lower KC (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	if (macdBearishCross && nearUpperKC) {
		if (lastSignal !== 'SELL') {
			return {
				signal: 'SELL',
				reason: `MACD bearish crossover + price near upper KC (strength: ${strength.toFixed(4)})`,
				confidence
			}
		}
	}

	return { signal: 'HOLD', reason: 'No crossover or duplicate signal', confidence }
}
